import { callPayloadAPI } from './payload-api';
import { createEnvironmentLogger } from '@kit/shared/logger';
const logger = createEnvironmentLogger('SURVEY-API');
/**
 * Get a survey by slug
 * @param slug The slug of the survey
 * @param supabaseClient Optional Supabase client (for client-side usage)
 * @returns The survey data
 */
export async function getSurvey(slug, supabaseClient) {
    logger.debug(`Getting survey with slug: ${slug}`);
    const result = await callPayloadAPI(`surveys?where[slug][equals]=${slug}&depth=3`, {}, supabaseClient);
    // Only log full results in development
    if (process.env.NODE_ENV === 'development') {
        logger.debug(`Survey result for slug ${slug}:`, result);
    }
    else {
        logger.info(`Retrieved survey: ${slug}`);
    }
    return result;
}
/**
 * Get questions for a survey
 * @param surveyId The ID of the survey
 * @param supabaseClient Optional Supabase client (for client-side usage)
 * @returns The survey questions
 */
export async function getSurveyQuestions(surveyId, supabaseClient) {
    var _a, _b, _c, _d;
    console.log(`Getting survey questions for survey ID: ${surveyId}`);
    try {
        // First, try to get the survey with its questions using a higher depth
        // This ensures we get the full question data
        const survey = await callPayloadAPI(`surveys/${surveyId}?depth=3`, {}, supabaseClient);
        console.log(`Survey data retrieved for ID ${surveyId}`);
        // If the survey has questions with full data, return them directly
        if (((_a = survey === null || survey === void 0 ? void 0 : survey.questions) === null || _a === void 0 ? void 0 : _a.length) && survey.questions[0].text) {
            console.log(`Found ${survey.questions.length} fully populated questions`);
            return { docs: survey.questions };
        }
        // If we have question IDs but not full data, fetch them directly
        if ((_b = survey === null || survey === void 0 ? void 0 : survey.questions) === null || _b === void 0 ? void 0 : _b.length) {
            console.log(`Found ${survey.questions.length} question references, fetching full data`);
            // Extract question IDs, handling different possible formats
            const questionIds = survey.questions
                .map((q) => {
                if (typeof q === 'string')
                    return q;
                if (q.id)
                    return q.id;
                if (q.value)
                    return q.value;
                return null;
            })
                .filter(Boolean)
                .join(',');
            console.log(`Question IDs: ${questionIds}`);
            if (questionIds) {
                const questionsResponse = await callPayloadAPI(`survey_questions?where[id][in]=${questionIds}&sort=position&limit=100`, {}, supabaseClient);
                if ((_c = questionsResponse === null || questionsResponse === void 0 ? void 0 : questionsResponse.docs) === null || _c === void 0 ? void 0 : _c.length) {
                    console.log(`Retrieved ${questionsResponse.docs.length} questions by ID`);
                    return questionsResponse;
                }
            }
        }
        // Skip the relationship tables approach since it's causing 404 errors
        console.log('Skipping relationship tables approach due to potential API limitations');
        // As a last resort, get all questions and filter by survey ID
        console.log('Trying to get all questions and filter by survey ID');
        const allQuestionsResponse = await callPayloadAPI('survey_questions?limit=100', {}, supabaseClient);
        if ((_d = allQuestionsResponse === null || allQuestionsResponse === void 0 ? void 0 : allQuestionsResponse.docs) === null || _d === void 0 ? void 0 : _d.length) {
            console.log(`Retrieved ${allQuestionsResponse.docs.length} total questions`);
            // Log all question IDs for debugging
            allQuestionsResponse.docs.forEach((q) => {
                var _a;
                console.log(`Question ID: ${q.id}, Text: ${(_a = q.text) === null || _a === void 0 ? void 0 : _a.substring(0, 30)}...`);
            });
            // Try to directly fetch the questions we know should be associated with this survey
            // This is a workaround based on the database query results
            if (surveyId === '6f463bef-d7a0-4e5a-b0fa-a789b5d6f0e0') {
                console.log('Special handling for Three Quick Questions survey');
                const knownQuestionIds = [
                    '61a8e0b5-c600-49cc-9b18-6ba0f158bed3',
                    'e0a592e6-d96a-4b62-ad11-3d6e16b2175d',
                    'e0b335b6-dde9-4117-963b-c482b3ae5595',
                ];
                const hardcodedQuestions = allQuestionsResponse.docs.filter((q) => knownQuestionIds.includes(q.id));
                if (hardcodedQuestions.length > 0) {
                    console.log(`Found ${hardcodedQuestions.length} hardcoded questions for Three Quick Questions survey`);
                    return Object.assign(Object.assign({}, allQuestionsResponse), { docs: hardcodedQuestions });
                }
            }
            // Special handling for Self-Assessment survey
            else if (surveyId === '5e352ade-c6a9-4e4a-9ffa-9680a5d5f9e9') {
                console.log('Special handling for Self-Assessment survey');
                // These IDs were retrieved from the database query
                const knownQuestionIds = [
                    'c259ffaf-2851-4e75-b368-286da3fb5e49',
                    'a1a78937-2700-4f9e-adb1-2769b16f0271',
                    '6487631a-6e82-4e6f-9fa4-1e9a86c77f0b',
                    '029b7739-7f08-4193-ae9e-9ab480cecd07',
                    'ac2d821d-7c64-4ceb-8cfb-72baac04432f',
                    '2f7e64ab-05ff-47c3-8ac7-a526333e433b',
                    'f27d6ef9-7963-4fb9-9bf8-7493d2edfa32',
                    '198fae0d-1891-491f-b1f5-b1d55643421a',
                    '825df7aa-1f91-4a47-a16b-23e55e528335',
                    '43595987-c680-4502-9daa-499962f83988',
                    'c904ab82-8a40-4c63-b104-1caf34e016c3',
                    'e4451157-1e71-4974-9cf9-8bdea1e6d88c',
                    'ddce46e1-d621-4f07-b91a-8bba63d01189',
                    'fd4b30e5-59a8-4a01-9366-9d77adb86e02',
                    'b5ff642a-4794-42c2-81e1-ea00f553a63f',
                    'c23be367-313d-417e-a4ff-1aa5271b6100',
                    'eff8300b-ddd5-46a4-bb23-61e82d4b7f4b',
                    '810c9f24-f043-4c76-9a08-2da83a8e926a',
                    'e504dd5d-df08-4e99-9460-faeca65de032',
                    '4192b1ea-ca8f-4cda-a419-5aa8ad6720f6',
                    '72ebe12b-9bf3-49c3-a9c3-a632fe991d2c',
                    '6adc4e59-de54-48f3-a93f-4322b5739546',
                    'db4e08bb-856a-42cd-b208-64e8b5037c48',
                    'beadf97b-0a41-4130-97e5-b4bd5399e733',
                    'f89404ae-0a14-4c55-b9fc-4eadb59a9dd5',
                ];
                const hardcodedQuestions = allQuestionsResponse.docs.filter((q) => knownQuestionIds.includes(q.id));
                if (hardcodedQuestions.length > 0) {
                    console.log(`Found ${hardcodedQuestions.length} hardcoded questions for Self-Assessment survey`);
                    return Object.assign(Object.assign({}, allQuestionsResponse), { docs: hardcodedQuestions });
                }
            }
            // Check for any relationship to the survey
            const filteredQuestions = allQuestionsResponse.docs.filter((q) => {
                // Log the question's relationship fields for debugging
                console.log(`Checking question ${q.id} relationships:`, {
                    surveys_id: q.surveys_id,
                    surveys_id_id: q.surveys_id_id,
                    surveys: Array.isArray(q.surveys) ? q.surveys.length : 'not an array',
                });
                // Direct relationship fields
                if (q.surveys_id === surveyId || q.surveys_id_id === surveyId) {
                    console.log(`Question ${q.id} matched by direct relationship`);
                    return true;
                }
                // Check surveys array if it exists
                if (Array.isArray(q.surveys)) {
                    const hasRelationship = q.surveys.some((s) => {
                        if (s === surveyId)
                            return true;
                        if (typeof s === 'object' &&
                            (s.id === surveyId || s.value === surveyId))
                            return true;
                        return false;
                    });
                    if (hasRelationship) {
                        console.log(`Question ${q.id} matched by surveys array`);
                    }
                    return hasRelationship;
                }
                return false;
            });
            console.log(`Filtered ${filteredQuestions.length} questions for survey ID ${surveyId}`);
            if (filteredQuestions.length > 0) {
                return Object.assign(Object.assign({}, allQuestionsResponse), { docs: filteredQuestions });
            }
            // If we still couldn't find any questions, try a different approach
            // Look for questions that might have the survey relationship in a different format
            console.log('Trying alternative relationship formats');
            const alternativeFilteredQuestions = allQuestionsResponse.docs.filter((q) => {
                // Check all properties for any that might contain the survey ID
                for (const key in q) {
                    if (typeof q[key] === 'string' && q[key] === surveyId) {
                        console.log(`Question ${q.id} matched by property ${key}`);
                        return true;
                    }
                    if (typeof q[key] === 'object' && q[key] !== null) {
                        // Check if the property is an object that contains the survey ID
                        if (q[key].id === surveyId || q[key].value === surveyId) {
                            console.log(`Question ${q.id} matched by object property ${key}`);
                            return true;
                        }
                        // Check if the property is an array that contains the survey ID
                        if (Array.isArray(q[key])) {
                            const hasMatch = q[key].some((item) => {
                                if (typeof item === 'string' && item === surveyId)
                                    return true;
                                if (typeof item === 'object' &&
                                    (item.id === surveyId || item.value === surveyId))
                                    return true;
                                return false;
                            });
                            if (hasMatch) {
                                console.log(`Question ${q.id} matched by array property ${key}`);
                                return true;
                            }
                        }
                    }
                }
                return false;
            });
            if (alternativeFilteredQuestions.length > 0) {
                console.log(`Found ${alternativeFilteredQuestions.length} questions using alternative filtering`);
                return Object.assign(Object.assign({}, allQuestionsResponse), { docs: alternativeFilteredQuestions });
            }
        }
    }
    catch (error) {
        console.error(`Error getting survey questions for survey ID ${surveyId}:`, error);
    }
    // If all attempts fail, return an empty result
    console.log('No questions found after all attempts');
    return { docs: [] };
}
// The following functions are deprecated as we now use Supabase directly
// They are kept here for backwards compatibility but will be removed in a future version
/**
 * @deprecated Use Supabase directly instead
 */
export async function getUserSurveyResponse(userId, surveyId) {
    console.warn('getUserSurveyResponse is deprecated. Use Supabase directly instead.');
    return { docs: [] };
}
/**
 * @deprecated Use Supabase directly instead
 */
export async function createSurveyResponse(data) {
    console.warn('createSurveyResponse is deprecated. Use Supabase directly instead.');
    return { id: null };
}
/**
 * @deprecated Use Supabase directly instead
 */
export async function updateSurveyResponse(id, data) {
    console.warn('updateSurveyResponse is deprecated. Use Supabase directly instead.');
    return { id };
}
/**
 * @deprecated Use Supabase directly instead
 */
export async function completeSurvey(id, data) {
    console.warn('completeSurvey is deprecated. Use Supabase directly instead.');
    return { id };
}
