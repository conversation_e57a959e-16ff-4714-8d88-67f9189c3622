{"name": "payload", "version": "3.39.1", "description": "A blank template to get started with Payload 3.0", "license": "MIT", "type": "module", "scripts": {"verify-config": "dotenv -e .env -- node scripts/verify-config.js", "verify-config:dev": "dotenv -e .env.development -- node scripts/verify-config.js", "verify-config:prod": "dotenv -e .env.production -- node scripts/verify-config.js", "build": "cross-env NODE_OPTIONS=--no-deprecation next build", "dev": "cross-env PORT=3020 NODE_OPTIONS=--no-deprecation next dev", "devsafe": "rm -rf .next && cross-env NODE_OPTIONS=--no-deprecation next dev", "generate:importmap": "cross-env NODE_OPTIONS=--no-deprecation payload generate:importmap", "generate:types": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts NODE_OPTIONS=--no-deprecation payload generate:types", "lint": "cross-env NODE_OPTIONS=--no-deprecation next lint", "payload": "cross-env PAYLOAD_CONFIG_PATH=src/payload.config.ts NODE_OPTIONS=--no-deprecation payload", "start": "cross-env NODE_OPTIONS=--no-deprecation next start", "print-env": "echo PAYLOAD_SECRET=$PAYLOAD_SECRET && echo DATABASE_URI=$DATABASE_URI && echo PAYLOAD_PUBLIC_SERVER_URL=$PAYLOAD_PUBLIC_SERVER_URL", "clean:init-scripts": "node -e \"require('fs').rmSync('./dist', { recursive: true, force: true })\"", "build:init-scripts": "node -e \"require('fs').rmSync('./dist', { recursive: true, force: true })\" && pnpm exec tsc --project tsconfig.init-scripts.json --listEmittedFiles --extendedDiagnostics && xcopy src\\init-scripts\\data dist\\src\\init-scripts\\data /E /I /Y", "preinit:data": "pnpm run build:init-scripts", "init:data": "node dist/src/init-scripts/initialize-payload-data.js"}, "dependencies": {"@kit/shared": "workspace:*", "@lexical/list": "^0.28.0", "@lexical/rich-text": "^0.28.0", "@markdoc/markdoc": "^0.5.2", "@payloadcms/db-postgres": "^3.39.1", "@payloadcms/next": "^3.39.1", "@payloadcms/payload-cloud": "^3.39.1", "@payloadcms/plugin-nested-docs": "^3.39.1", "@payloadcms/richtext-lexical": "^3.39.1", "@payloadcms/storage-s3": "^3.39.1", "cross-env": "^7.0.3", "execa": "^9.5.3", "graphql": "^16.8.1", "gray-matter": "^4.0.3", "js-yaml": "^4.1.0", "lexical": "^0.28.0", "next": "15.3.2", "payload": "^3.39.1", "react": "19.1.0", "react-dom": "19.1.0", "sharp": "0.32.6", "uuid": "^9.0.1", "zod": "^3.25.7"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/fs-extra": "^11.0.4", "@types/js-yaml": "^4.0.9", "@types/node": "^22.15.19", "@types/pg": "^8.11.11", "@types/react": "19.1.4", "@types/react-dom": "19.1.5", "@types/uuid": "^9.0.7", "@types/yargs": "^17.0.33", "copyfiles": "^2.4.1", "dotenv": "16.5.0", "dotenv-cli": "^8.0.0", "eslint": "^9.27.0", "eslint-config-next": "15.3.1", "fs-extra": "^11.3.0", "pg": "^8.14.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node": "^10.9.1", "typescript": "^5.8.3", "yargs": "^17.7.2"}, "engines": {"node": "^18.20.2 || >=20.9.0", "pnpm": "^9 || ^10"}}