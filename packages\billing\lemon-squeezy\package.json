{"name": "@kit/lemon-squeezy", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts"}, "dependencies": {"@lemonsqueezy/lemonsqueezy.js": "4.0.0"}, "devDependencies": {"@kit/billing": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@types/node": "^22.15.0", "@types/react": "19.1.6", "next": "15.3.2", "react": "19.1.0", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}