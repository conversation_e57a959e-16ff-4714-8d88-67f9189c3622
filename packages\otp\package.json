{"name": "@kit/otp", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/api/index.ts", "./components": "./src/components/index.ts"}, "devDependencies": {"@hookform/resolvers": "^5.0.1", "@kit/email-templates": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/mailers": "workspace:*", "@kit/next": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.49.8", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}