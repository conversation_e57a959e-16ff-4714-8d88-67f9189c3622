{"name": "@kit/billing", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components/*": "./src/components/*", "./schema": "./src/schema/index.ts", "./types": "./src/types/index.ts"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}