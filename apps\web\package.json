{"name": "web", "version": "0.1.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"analyze": "ANALYZE=true pnpm run build", "build": "next build", "build:test": "NODE_ENV=test next build --turbopack", "clean": "git clean -xdf .next .turbo node_modules", "dev": "next dev --turbo | pino-pretty -c", "lint": "eslint .", "lint:fix": "next lint --fix", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,md,json}\"", "start": "next start", "start:test": "NODE_ENV=test next start", "typecheck": "tsc --noEmit", "supabase": "supabase", "supabase:start": "supabase status || supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:status": "supabase status", "supabase:test": "supabase db test", "supabase:db:lint": "supabase db lint", "supabase:db:diff": "supabase db diff", "supabase:deploy": "supabase link --project-ref $SUPABASE_PROJECT_REF && supabase db push", "supabase:typegen": "pnpm run supabase:typegen:packages && pnpm run supabase:typegen:app", "supabase:typegen:packages": "supabase gen types typescript --local > ../../packages/supabase/src/database.types.ts", "supabase:typegen:app": "supabase gen types typescript --local > ./lib/database.types.ts", "supabase:db:dump:local": "supabase db dump --local --data-only"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@edge-csrf/nextjs": "2.5.3-cloudflare-rc1", "@hookform/resolvers": "^5.0.1", "@kit/accounts": "workspace:*", "@kit/admin": "workspace:*", "@kit/ai-gateway": "workspace:*", "@kit/analytics": "workspace:*", "@kit/auth": "workspace:*", "@kit/billing": "workspace:*", "@kit/billing-gateway": "workspace:*", "@kit/cms": "workspace:*", "@kit/database-webhooks": "workspace:*", "@kit/email-templates": "workspace:*", "@kit/i18n": "workspace:*", "@kit/mailers": "workspace:*", "@kit/monitoring": "workspace:*", "@kit/next": "workspace:*", "@kit/notifications": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/team-accounts": "workspace:*", "@kit/testimonial": "workspace:*", "@kit/ui": "workspace:*", "@makerkit/data-loader-supabase-core": "^0.0.10", "@makerkit/data-loader-supabase-nextjs": "^1.2.5", "@marsidev/react-turnstile": "^1.1.0", "@nosecone/next": "1.0.0-beta.7", "@payloadcms/db-postgres": "^3.39.1", "@radix-ui/react-icons": "^1.3.2", "@supabase/supabase-js": "2.49.8", "@tanstack/react-query": "5.77.2", "@tanstack/react-table": "^8.21.3", "@tiptap/extension-bold": "^2.11.5", "@tiptap/extension-bullet-list": "^2.11.5", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-italic": "^2.11.5", "@tiptap/extension-list-item": "^2.11.5", "@tiptap/extension-ordered-list": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/lodash": "^4.17.15", "@types/uuid": "^9.0.7", "@vercel/blob": "^0.27.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lodash": "^4.17.21", "lucide-react": "^0.511.0", "next": "15.3.2", "next-sitemap": "^4.2.3", "next-themes": "0.4.6", "pino": "^9.6.0", "pptxgenjs": "^3.12.0", "react": "19.1.0", "react-confetti": "^6.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "recharts": "2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "zod": "^3.25.31"}, "devDependencies": {"@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@next/bundle-analyzer": "15.3.2", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^22.15.23", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "babel-plugin-react-compiler": "19.1.0-rc.2", "cssnano": "^7.0.7", "pino-pretty": "^13.0.0", "prettier": "^3.5.3", "supabase": "^2.23.4", "tailwindcss": "4.1.7", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}, "prettier": "@kit/prettier-config", "browserslist": ["last 1 versions", "> 0.7%", "not dead"], "overrides": {"motion": {"react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106"}}}