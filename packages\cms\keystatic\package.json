{"name": "@kit/keystatic", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./renderer": "./src/content-renderer.tsx", "./admin": "./src/keystatic-admin.tsx", "./route-handler": "./src/keystatic-route-handler.ts"}, "dependencies": {"@keystatic/core": "0.5.47", "@keystatic/next": "^5.0.4", "@markdoc/markdoc": "^0.5.2"}, "devDependencies": {"@kit/cms-types": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@types/node": "^22.15.23", "@types/react": "19.1.6", "react": "19.1.0", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}