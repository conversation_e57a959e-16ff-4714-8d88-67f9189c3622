# Production Environment Variables for Payload CLI Migration

# Database Configuration (REQUIRED)
DATABASE_URI=postgresql://postgres.ldebzombxtszzcgnylgq:<EMAIL>:6543/postgres?sslmode=prefer

# Payload Configuration (REQUIRED)
PAYLOAD_SECRET=abcfedd4-6912-42ae-b4c9-b29101294a00

# Production Migration Settings
NODE_ENV=production
PAYLOAD_MIGRATION_MODE=production
PAYLOAD_ENABLE_SSL=true

# Server Configuration
PAYLOAD_PUBLIC_SERVER_URL=https://payload.slideheroes.com

# Optional: Logging Configuration
LOG_LEVEL=info
PAYLOAD_DEBUG=false

# Optional: Enhanced SSL Configuration
# PAYLOAD_ENABLE_SSL=true (already set above)

# Optional: Database Health Monitoring
ENABLE_DB_HEALTH_MONITORING=true
DB_HEALTH_CHECK_INTERVAL=30000

# R2 Storage Configuration (REQUIRED for Production)
R2_ACCESS_KEY_ID=01e0dc066629ed210d479b7aca40f823
R2_SECRET_ACCESS_KEY=df8c3914c61b29757b2b60ac95c9e148662b0af4bd0092006e9a67f9eaba02e8
R2_ENDPOINT=https://d33fc17df32ce7d9d48eb8045f1d340a.r2.cloudflarestorage.com
R2_REGION=auto
R2_ACCOUNT_ID=d33fc17df32ce7d9d48eb8045f1d340a

# R2 Buckets
R2_MEDIA_BUCKET=media
R2_DOWNLOADS_BUCKET=downloads

# Optional: Public base URLs for production
PAYLOAD_PUBLIC_MEDIA_BASE_URL=https://media.slideheroes.com
PAYLOAD_PUBLIC_DOWNLOADS_BASE_URL=https://downloads.slideheroes.com