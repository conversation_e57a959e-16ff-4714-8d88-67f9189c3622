{"name": "@kit/billing-gateway", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts", "./checkout": "./src/components/embedded-checkout.tsx", "./marketing": "./src/components/marketing.tsx"}, "devDependencies": {"@hookform/resolvers": "^5.0.1", "@kit/billing": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/lemon-squeezy": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/stripe": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@supabase/supabase-js": "2.49.8", "@types/react": "19.1.6", "date-fns": "^4.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "19.1.0", "react-hook-form": "^7.56.4", "react-i18next": "^15.5.2", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}