# Development environment variables for Payload CMS

# Environment
NODE_ENV=development

# Database connection string for Supabase (development)
DATABASE_URI=postgresql://postgres:postgres@localhost:54322/postgres?schema=payload

# Secret key for Payload CMS
PAYLOAD_SECRET=abcfedd4-6912-42ae-b4c9-b29101294a00

# Server URL for Payload CMS (development)
PAYLOAD_PUBLIC_SERVER_URL=http://localhost:3020

# Supabase Configuration
SUPABASE_URL=http://localhost:54321
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU
SUPABASE_DB_WEBHOOK_SECRET=6058a23e-a2a9-4794-ae1b-b29101311074

# R2 Configuration for Development
R2_MEDIA_BUCKET=media
R2_ACCESS_KEY_ID=7e6826129bd020f755f213684bb2e038
R2_SECRET_ACCESS_KEY=ee762c51aa7b9a3893bc9dca4b4085ae5d74fd611a436ed158c571d13785cd0c
R2_ENDPOINT=https://d33fc17df32ce7d9d48eb8045f1d340a.r2.cloudflarestorage.com
R2_REGION=auto
R2_ACCOUNT_ID=d33fc17df32ce7d9d48eb8045f1d340a
