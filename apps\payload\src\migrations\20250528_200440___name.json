{"id": "c88cad57-f016-457c-9678-bf9fc14ca646", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"payload.users": {"name": "users", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "enum_users_role", "typeSchema": "payload", "primaryKey": false, "notNull": true, "default": "'user'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "reset_password_token": {"name": "reset_password_token", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "reset_password_expiration": {"name": "reset_password_expiration", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "salt": {"name": "salt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "hash": {"name": "hash", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "lock_until": {"name": "lock_until", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}}, "indexes": {"users_updated_at_idx": {"name": "users_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.media_tags": {"name": "media_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_tags_order_idx": {"name": "media_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_tags_parent_id_idx": {"name": "media_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"media_tags_parent_id_fk": {"name": "media_tags_parent_id_fk", "tableFrom": "media_tags", "tableTo": "media", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.media": {"name": "media", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "alt": {"name": "alt", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "caption": {"name": "caption", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "enum_media_type", "typeSchema": "payload", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_url": {"name": "sizes_thumbnail_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_width": {"name": "sizes_thumbnail_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_height": {"name": "sizes_thumbnail_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_mime_type": {"name": "sizes_thumbnail_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filesize": {"name": "sizes_thumbnail_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_thumbnail_filename": {"name": "sizes_thumbnail_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_url": {"name": "sizes_card_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_width": {"name": "sizes_card_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_height": {"name": "sizes_card_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_mime_type": {"name": "sizes_card_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_card_filesize": {"name": "sizes_card_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_card_filename": {"name": "sizes_card_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_url": {"name": "sizes_hero_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_width": {"name": "sizes_hero_width", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_height": {"name": "sizes_hero_height", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_mime_type": {"name": "sizes_hero_mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "sizes_hero_filesize": {"name": "sizes_hero_filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "sizes_hero_filename": {"name": "sizes_hero_filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"media_updated_at_idx": {"name": "media_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_created_at_idx": {"name": "media_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_filename_idx": {"name": "media_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_thumbnail_sizes_thumbnail_filename_idx": {"name": "media_sizes_thumbnail_sizes_thumbnail_filename_idx", "columns": [{"expression": "sizes_thumbnail_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_card_sizes_card_filename_idx": {"name": "media_sizes_card_sizes_card_filename_idx", "columns": [{"expression": "sizes_card_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sizes_hero_sizes_hero_filename_idx": {"name": "media_sizes_hero_sizes_hero_filename_idx", "columns": [{"expression": "sizes_hero_filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.downloads_tags": {"name": "downloads_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"downloads_tags_order_idx": {"name": "downloads_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "downloads_tags_parent_id_idx": {"name": "downloads_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"downloads_tags_parent_id_fk": {"name": "downloads_tags_parent_id_fk", "tableFrom": "downloads_tags", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.downloads": {"name": "downloads", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "enum_downloads_category", "typeSchema": "payload", "primaryKey": false, "notNull": false}, "download_count": {"name": "download_count", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "access_level": {"name": "access_level", "type": "enum_downloads_access_level", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'public'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "thumbnail_u_r_l": {"name": "thumbnail_u_r_l", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "filesize": {"name": "filesize", "type": "numeric", "primaryKey": false, "notNull": false}, "width": {"name": "width", "type": "numeric", "primaryKey": false, "notNull": false}, "height": {"name": "height", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_x": {"name": "focal_x", "type": "numeric", "primaryKey": false, "notNull": false}, "focal_y": {"name": "focal_y", "type": "numeric", "primaryKey": false, "notNull": false}}, "indexes": {"downloads_updated_at_idx": {"name": "downloads_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "downloads_created_at_idx": {"name": "downloads_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "downloads_filename_idx": {"name": "downloads_filename_idx", "columns": [{"expression": "filename", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.posts_categories": {"name": "posts_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"posts_categories_order_idx": {"name": "posts_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_categories_parent_id_idx": {"name": "posts_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_categories_parent_id_fk": {"name": "posts_categories_parent_id_fk", "tableFrom": "posts_categories", "tableTo": "posts", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.posts_tags": {"name": "posts_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"posts_tags_order_idx": {"name": "posts_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_tags_parent_id_idx": {"name": "posts_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_tags_parent_id_fk": {"name": "posts_tags_parent_id_fk", "tableFrom": "posts_tags", "tableTo": "posts", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.posts": {"name": "posts", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "image_id_id": {"name": "image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_posts_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_posts_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"posts_image_id_idx": {"name": "posts_image_id_idx", "columns": [{"expression": "image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_updated_at_idx": {"name": "posts_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_created_at_idx": {"name": "posts_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts__status_idx": {"name": "posts__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_image_id_id_media_id_fk": {"name": "posts_image_id_id_media_id_fk", "tableFrom": "posts", "tableTo": "media", "schemaTo": "payload", "columnsFrom": ["image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.posts_rels": {"name": "posts_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"posts_rels_order_idx": {"name": "posts_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_parent_idx": {"name": "posts_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_path_idx": {"name": "posts_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "posts_rels_downloads_id_idx": {"name": "posts_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"posts_rels_parent_fk": {"name": "posts_rels_parent_fk", "tableFrom": "posts_rels", "tableTo": "posts", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "posts_rels_downloads_fk": {"name": "posts_rels_downloads_fk", "tableFrom": "posts_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._posts_v_version_categories": {"name": "_posts_v_version_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_version_categories_order_idx": {"name": "_posts_v_version_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_categories_parent_id_idx": {"name": "_posts_v_version_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_version_categories_parent_id_fk": {"name": "_posts_v_version_categories_parent_id_fk", "tableFrom": "_posts_v_version_categories", "tableTo": "_posts_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._posts_v_version_tags": {"name": "_posts_v_version_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_version_tags_order_idx": {"name": "_posts_v_version_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_tags_parent_id_idx": {"name": "_posts_v_version_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_version_tags_parent_id_fk": {"name": "_posts_v_version_tags_parent_id_fk", "tableFrom": "_posts_v_version_tags", "tableTo": "_posts_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._posts_v": {"name": "_posts_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_image_id_id": {"name": "version_image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__posts_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__posts_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_parent_idx": {"name": "_posts_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_image_id_idx": {"name": "_posts_v_version_version_image_id_idx", "columns": [{"expression": "version_image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_updated_at_idx": {"name": "_posts_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version_created_at_idx": {"name": "_posts_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_version_version__status_idx": {"name": "_posts_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_created_at_idx": {"name": "_posts_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_updated_at_idx": {"name": "_posts_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_latest_idx": {"name": "_posts_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_parent_id_posts_id_fk": {"name": "_posts_v_parent_id_posts_id_fk", "tableFrom": "_posts_v", "tableTo": "posts", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_posts_v_version_image_id_id_media_id_fk": {"name": "_posts_v_version_image_id_id_media_id_fk", "tableFrom": "_posts_v", "tableTo": "media", "schemaTo": "payload", "columnsFrom": ["version_image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._posts_v_rels": {"name": "_posts_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_posts_v_rels_order_idx": {"name": "_posts_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_parent_idx": {"name": "_posts_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_path_idx": {"name": "_posts_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_posts_v_rels_downloads_id_idx": {"name": "_posts_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_posts_v_rels_parent_fk": {"name": "_posts_v_rels_parent_fk", "tableFrom": "_posts_v_rels", "tableTo": "_posts_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_posts_v_rels_downloads_fk": {"name": "_posts_v_rels_downloads_fk", "tableFrom": "_posts_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.documentation_categories": {"name": "documentation_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"documentation_categories_order_idx": {"name": "documentation_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_categories_parent_id_idx": {"name": "documentation_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documentation_categories_parent_id_fk": {"name": "documentation_categories_parent_id_fk", "tableFrom": "documentation_categories", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.documentation_tags": {"name": "documentation_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"documentation_tags_order_idx": {"name": "documentation_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_tags_parent_id_idx": {"name": "documentation_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documentation_tags_parent_id_fk": {"name": "documentation_tags_parent_id_fk", "tableFrom": "documentation_tags", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.documentation_breadcrumbs": {"name": "documentation_breadcrumbs", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "doc_id": {"name": "doc_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"documentation_breadcrumbs_order_idx": {"name": "documentation_breadcrumbs_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_breadcrumbs_parent_id_idx": {"name": "documentation_breadcrumbs_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_breadcrumbs_doc_idx": {"name": "documentation_breadcrumbs_doc_idx", "columns": [{"expression": "doc_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documentation_breadcrumbs_doc_id_documentation_id_fk": {"name": "documentation_breadcrumbs_doc_id_documentation_id_fk", "tableFrom": "documentation_breadcrumbs", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["doc_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "documentation_breadcrumbs_parent_id_fk": {"name": "documentation_breadcrumbs_parent_id_fk", "tableFrom": "documentation_breadcrumbs", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.documentation": {"name": "documentation", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_documentation_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "order": {"name": "order", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_documentation_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"documentation_parent_idx": {"name": "documentation_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_updated_at_idx": {"name": "documentation_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_created_at_idx": {"name": "documentation_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation__status_idx": {"name": "documentation__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documentation_parent_id_documentation_id_fk": {"name": "documentation_parent_id_documentation_id_fk", "tableFrom": "documentation", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.documentation_rels": {"name": "documentation_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"documentation_rels_order_idx": {"name": "documentation_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_rels_parent_idx": {"name": "documentation_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_rels_path_idx": {"name": "documentation_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "documentation_rels_downloads_id_idx": {"name": "documentation_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"documentation_rels_parent_fk": {"name": "documentation_rels_parent_fk", "tableFrom": "documentation_rels", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "documentation_rels_downloads_fk": {"name": "documentation_rels_downloads_fk", "tableFrom": "documentation_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._documentation_v_version_categories": {"name": "_documentation_v_version_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_documentation_v_version_categories_order_idx": {"name": "_documentation_v_version_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_categories_parent_id_idx": {"name": "_documentation_v_version_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_documentation_v_version_categories_parent_id_fk": {"name": "_documentation_v_version_categories_parent_id_fk", "tableFrom": "_documentation_v_version_categories", "tableTo": "_documentation_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._documentation_v_version_tags": {"name": "_documentation_v_version_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_documentation_v_version_tags_order_idx": {"name": "_documentation_v_version_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_tags_parent_id_idx": {"name": "_documentation_v_version_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_documentation_v_version_tags_parent_id_fk": {"name": "_documentation_v_version_tags_parent_id_fk", "tableFrom": "_documentation_v_version_tags", "tableTo": "_documentation_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._documentation_v_version_breadcrumbs": {"name": "_documentation_v_version_breadcrumbs", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "doc_id": {"name": "doc_id", "type": "uuid", "primaryKey": false, "notNull": false}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "label": {"name": "label", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_documentation_v_version_breadcrumbs_order_idx": {"name": "_documentation_v_version_breadcrumbs_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_breadcrumbs_parent_id_idx": {"name": "_documentation_v_version_breadcrumbs_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_breadcrumbs_doc_idx": {"name": "_documentation_v_version_breadcrumbs_doc_idx", "columns": [{"expression": "doc_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_documentation_v_version_breadcrumbs_doc_id_documentation_id_fk": {"name": "_documentation_v_version_breadcrumbs_doc_id_documentation_id_fk", "tableFrom": "_documentation_v_version_breadcrumbs", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["doc_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_documentation_v_version_breadcrumbs_parent_id_fk": {"name": "_documentation_v_version_breadcrumbs_parent_id_fk", "tableFrom": "_documentation_v_version_breadcrumbs", "tableTo": "_documentation_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._documentation_v": {"name": "_documentation_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__documentation_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_order": {"name": "version_order", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "version_parent_id": {"name": "version_parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__documentation_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_documentation_v_parent_idx": {"name": "_documentation_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_version_parent_idx": {"name": "_documentation_v_version_version_parent_idx", "columns": [{"expression": "version_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_version_updated_at_idx": {"name": "_documentation_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_version_created_at_idx": {"name": "_documentation_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_version_version__status_idx": {"name": "_documentation_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_created_at_idx": {"name": "_documentation_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_updated_at_idx": {"name": "_documentation_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_latest_idx": {"name": "_documentation_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_documentation_v_parent_id_documentation_id_fk": {"name": "_documentation_v_parent_id_documentation_id_fk", "tableFrom": "_documentation_v", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_documentation_v_version_parent_id_documentation_id_fk": {"name": "_documentation_v_version_parent_id_documentation_id_fk", "tableFrom": "_documentation_v", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["version_parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._documentation_v_rels": {"name": "_documentation_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_documentation_v_rels_order_idx": {"name": "_documentation_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_rels_parent_idx": {"name": "_documentation_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_rels_path_idx": {"name": "_documentation_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_documentation_v_rels_downloads_id_idx": {"name": "_documentation_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_documentation_v_rels_parent_fk": {"name": "_documentation_v_rels_parent_fk", "tableFrom": "_documentation_v_rels", "tableTo": "_documentation_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_documentation_v_rels_downloads_fk": {"name": "_documentation_v_rels_downloads_fk", "tableFrom": "_documentation_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.private_categories": {"name": "private_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"private_categories_order_idx": {"name": "private_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_categories_parent_id_idx": {"name": "private_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"private_categories_parent_id_fk": {"name": "private_categories_parent_id_fk", "tableFrom": "private_categories", "tableTo": "private", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.private_tags": {"name": "private_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"private_tags_order_idx": {"name": "private_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_tags_parent_id_idx": {"name": "private_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"private_tags_parent_id_fk": {"name": "private_tags_parent_id_fk", "tableFrom": "private_tags", "tableTo": "private", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.private": {"name": "private", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "image_id_id": {"name": "image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "featured_image_id_id": {"name": "featured_image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_private_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_private_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"private_image_id_idx": {"name": "private_image_id_idx", "columns": [{"expression": "image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_featured_image_id_idx": {"name": "private_featured_image_id_idx", "columns": [{"expression": "featured_image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_updated_at_idx": {"name": "private_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_created_at_idx": {"name": "private_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private__status_idx": {"name": "private__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"private_image_id_id_downloads_id_fk": {"name": "private_image_id_id_downloads_id_fk", "tableFrom": "private", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "private_featured_image_id_id_downloads_id_fk": {"name": "private_featured_image_id_id_downloads_id_fk", "tableFrom": "private", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["featured_image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.private_rels": {"name": "private_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"private_rels_order_idx": {"name": "private_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_rels_parent_idx": {"name": "private_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_rels_path_idx": {"name": "private_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "private_rels_downloads_id_idx": {"name": "private_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"private_rels_parent_fk": {"name": "private_rels_parent_fk", "tableFrom": "private_rels", "tableTo": "private", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "private_rels_downloads_fk": {"name": "private_rels_downloads_fk", "tableFrom": "private_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._private_v_version_categories": {"name": "_private_v_version_categories", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_private_v_version_categories_order_idx": {"name": "_private_v_version_categories_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_categories_parent_id_idx": {"name": "_private_v_version_categories_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_private_v_version_categories_parent_id_fk": {"name": "_private_v_version_categories_parent_id_fk", "tableFrom": "_private_v_version_categories", "tableTo": "_private_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._private_v_version_tags": {"name": "_private_v_version_tags", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tag": {"name": "tag", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_private_v_version_tags_order_idx": {"name": "_private_v_version_tags_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_tags_parent_id_idx": {"name": "_private_v_version_tags_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_private_v_version_tags_parent_id_fk": {"name": "_private_v_version_tags_parent_id_fk", "tableFrom": "_private_v_version_tags", "tableTo": "_private_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._private_v": {"name": "_private_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_image_id_id": {"name": "version_image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_featured_image_id_id": {"name": "version_featured_image_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__private_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__private_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_private_v_parent_idx": {"name": "_private_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_version_image_id_idx": {"name": "_private_v_version_version_image_id_idx", "columns": [{"expression": "version_image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_version_featured_image_id_idx": {"name": "_private_v_version_version_featured_image_id_idx", "columns": [{"expression": "version_featured_image_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_version_updated_at_idx": {"name": "_private_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_version_created_at_idx": {"name": "_private_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_version_version__status_idx": {"name": "_private_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_created_at_idx": {"name": "_private_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_updated_at_idx": {"name": "_private_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_latest_idx": {"name": "_private_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_private_v_parent_id_private_id_fk": {"name": "_private_v_parent_id_private_id_fk", "tableFrom": "_private_v", "tableTo": "private", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_private_v_version_image_id_id_downloads_id_fk": {"name": "_private_v_version_image_id_id_downloads_id_fk", "tableFrom": "_private_v", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["version_image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_private_v_version_featured_image_id_id_downloads_id_fk": {"name": "_private_v_version_featured_image_id_id_downloads_id_fk", "tableFrom": "_private_v", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["version_featured_image_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._private_v_rels": {"name": "_private_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_private_v_rels_order_idx": {"name": "_private_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_rels_parent_idx": {"name": "_private_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_rels_path_idx": {"name": "_private_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_private_v_rels_downloads_id_idx": {"name": "_private_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_private_v_rels_parent_fk": {"name": "_private_v_rels_parent_fk", "tableFrom": "_private_v_rels", "tableTo": "_private_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_private_v_rels_downloads_fk": {"name": "_private_v_rels_downloads_fk", "tableFrom": "_private_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.courses": {"name": "courses", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_courses_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_courses_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"courses_slug_idx": {"name": "courses_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "courses_updated_at_idx": {"name": "courses_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "courses_created_at_idx": {"name": "courses_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "courses__status_idx": {"name": "courses__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.courses_rels": {"name": "courses_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"courses_rels_order_idx": {"name": "courses_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "courses_rels_parent_idx": {"name": "courses_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "courses_rels_path_idx": {"name": "courses_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "courses_rels_downloads_id_idx": {"name": "courses_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"courses_rels_parent_fk": {"name": "courses_rels_parent_fk", "tableFrom": "courses_rels", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "courses_rels_downloads_fk": {"name": "courses_rels_downloads_fk", "tableFrom": "courses_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._courses_v": {"name": "_courses_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__courses_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__courses_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_courses_v_parent_idx": {"name": "_courses_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_version_version_slug_idx": {"name": "_courses_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_version_version_updated_at_idx": {"name": "_courses_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_version_version_created_at_idx": {"name": "_courses_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_version_version__status_idx": {"name": "_courses_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_created_at_idx": {"name": "_courses_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_updated_at_idx": {"name": "_courses_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_latest_idx": {"name": "_courses_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_courses_v_parent_id_courses_id_fk": {"name": "_courses_v_parent_id_courses_id_fk", "tableFrom": "_courses_v", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._courses_v_rels": {"name": "_courses_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_courses_v_rels_order_idx": {"name": "_courses_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_rels_parent_idx": {"name": "_courses_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_rels_path_idx": {"name": "_courses_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_courses_v_rels_downloads_id_idx": {"name": "_courses_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_courses_v_rels_parent_fk": {"name": "_courses_v_rels_parent_fk", "tableFrom": "_courses_v_rels", "tableTo": "_courses_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_courses_v_rels_downloads_fk": {"name": "_courses_v_rels_downloads_fk", "tableFrom": "_courses_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.course_lessons": {"name": "course_lessons", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "bunny_video_id": {"name": "bunny_video_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "bunny_library_id": {"name": "bunny_library_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'264486'"}, "video_source_type": {"name": "video_source_type", "type": "enum_course_lessons_video_source_type", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'youtube'"}, "youtube_video_id": {"name": "youtube_video_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "todo_complete_quiz": {"name": "todo_complete_quiz", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": false}, "lesson_number": {"name": "lesson_number", "type": "numeric", "primaryKey": false, "notNull": false}, "estimated_duration": {"name": "estimated_duration", "type": "numeric", "primaryKey": false, "notNull": false}, "course_id_id": {"name": "course_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "quiz_id_id": {"name": "quiz_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "survey_id_id": {"name": "survey_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_course_lessons_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"course_lessons_slug_idx": {"name": "course_lessons_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_course_id_idx": {"name": "course_lessons_course_id_idx", "columns": [{"expression": "course_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_quiz_id_idx": {"name": "course_lessons_quiz_id_idx", "columns": [{"expression": "quiz_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_survey_id_idx": {"name": "course_lessons_survey_id_idx", "columns": [{"expression": "survey_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_updated_at_idx": {"name": "course_lessons_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_created_at_idx": {"name": "course_lessons_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons__status_idx": {"name": "course_lessons__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_lessons_course_id_id_courses_id_fk": {"name": "course_lessons_course_id_id_courses_id_fk", "tableFrom": "course_lessons", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["course_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "course_lessons_quiz_id_id_course_quizzes_id_fk": {"name": "course_lessons_quiz_id_id_course_quizzes_id_fk", "tableFrom": "course_lessons", "tableTo": "course_quizzes", "schemaTo": "payload", "columnsFrom": ["quiz_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "course_lessons_survey_id_id_surveys_id_fk": {"name": "course_lessons_survey_id_id_surveys_id_fk", "tableFrom": "course_lessons", "tableTo": "surveys", "schemaTo": "payload", "columnsFrom": ["survey_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.course_lessons_rels": {"name": "course_lessons_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"course_lessons_rels_order_idx": {"name": "course_lessons_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_rels_parent_idx": {"name": "course_lessons_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_rels_path_idx": {"name": "course_lessons_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_lessons_rels_downloads_id_idx": {"name": "course_lessons_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_lessons_rels_parent_fk": {"name": "course_lessons_rels_parent_fk", "tableFrom": "course_lessons_rels", "tableTo": "course_lessons", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_lessons_rels_downloads_fk": {"name": "course_lessons_rels_downloads_fk", "tableFrom": "course_lessons_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._course_lessons_v": {"name": "_course_lessons_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_bunny_video_id": {"name": "version_bunny_video_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_bunny_library_id": {"name": "version_bunny_library_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false, "default": "'264486'"}, "version_video_source_type": {"name": "version_video_source_type", "type": "enum__course_lessons_v_version_video_source_type", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'youtube'"}, "version_youtube_video_id": {"name": "version_youtube_video_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_todo_complete_quiz": {"name": "version_todo_complete_quiz", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_content": {"name": "version_content", "type": "jsonb", "primaryKey": false, "notNull": false}, "version_lesson_number": {"name": "version_lesson_number", "type": "numeric", "primaryKey": false, "notNull": false}, "version_estimated_duration": {"name": "version_estimated_duration", "type": "numeric", "primaryKey": false, "notNull": false}, "version_course_id_id": {"name": "version_course_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_quiz_id_id": {"name": "version_quiz_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_survey_id_id": {"name": "version_survey_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__course_lessons_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_course_lessons_v_parent_idx": {"name": "_course_lessons_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_slug_idx": {"name": "_course_lessons_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_course_id_idx": {"name": "_course_lessons_v_version_version_course_id_idx", "columns": [{"expression": "version_course_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_quiz_id_idx": {"name": "_course_lessons_v_version_version_quiz_id_idx", "columns": [{"expression": "version_quiz_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_survey_id_idx": {"name": "_course_lessons_v_version_version_survey_id_idx", "columns": [{"expression": "version_survey_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_updated_at_idx": {"name": "_course_lessons_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version_created_at_idx": {"name": "_course_lessons_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_version_version__status_idx": {"name": "_course_lessons_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_created_at_idx": {"name": "_course_lessons_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_updated_at_idx": {"name": "_course_lessons_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_latest_idx": {"name": "_course_lessons_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_course_lessons_v_parent_id_course_lessons_id_fk": {"name": "_course_lessons_v_parent_id_course_lessons_id_fk", "tableFrom": "_course_lessons_v", "tableTo": "course_lessons", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_course_lessons_v_version_course_id_id_courses_id_fk": {"name": "_course_lessons_v_version_course_id_id_courses_id_fk", "tableFrom": "_course_lessons_v", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["version_course_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_course_lessons_v_version_quiz_id_id_course_quizzes_id_fk": {"name": "_course_lessons_v_version_quiz_id_id_course_quizzes_id_fk", "tableFrom": "_course_lessons_v", "tableTo": "course_quizzes", "schemaTo": "payload", "columnsFrom": ["version_quiz_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_course_lessons_v_version_survey_id_id_surveys_id_fk": {"name": "_course_lessons_v_version_survey_id_id_surveys_id_fk", "tableFrom": "_course_lessons_v", "tableTo": "surveys", "schemaTo": "payload", "columnsFrom": ["version_survey_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._course_lessons_v_rels": {"name": "_course_lessons_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_course_lessons_v_rels_order_idx": {"name": "_course_lessons_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_rels_parent_idx": {"name": "_course_lessons_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_rels_path_idx": {"name": "_course_lessons_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_lessons_v_rels_downloads_id_idx": {"name": "_course_lessons_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_course_lessons_v_rels_parent_fk": {"name": "_course_lessons_v_rels_parent_fk", "tableFrom": "_course_lessons_v_rels", "tableTo": "_course_lessons_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_course_lessons_v_rels_downloads_fk": {"name": "_course_lessons_v_rels_downloads_fk", "tableFrom": "_course_lessons_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.course_quizzes": {"name": "course_quizzes", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "course_id_id": {"name": "course_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "pass_threshold": {"name": "pass_threshold", "type": "numeric", "primaryKey": false, "notNull": false, "default": 70}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_course_quizzes_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"course_quizzes_slug_idx": {"name": "course_quizzes_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_course_id_idx": {"name": "course_quizzes_course_id_idx", "columns": [{"expression": "course_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_updated_at_idx": {"name": "course_quizzes_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_created_at_idx": {"name": "course_quizzes_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes__status_idx": {"name": "course_quizzes__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_quizzes_course_id_id_courses_id_fk": {"name": "course_quizzes_course_id_id_courses_id_fk", "tableFrom": "course_quizzes", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["course_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.course_quizzes_rels": {"name": "course_quizzes_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "quiz_questions_id": {"name": "quiz_questions_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"course_quizzes_rels_order_idx": {"name": "course_quizzes_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_rels_parent_idx": {"name": "course_quizzes_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_rels_path_idx": {"name": "course_quizzes_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "course_quizzes_rels_quiz_questions_id_idx": {"name": "course_quizzes_rels_quiz_questions_id_idx", "columns": [{"expression": "quiz_questions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"course_quizzes_rels_parent_fk": {"name": "course_quizzes_rels_parent_fk", "tableFrom": "course_quizzes_rels", "tableTo": "course_quizzes", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "course_quizzes_rels_quiz_questions_fk": {"name": "course_quizzes_rels_quiz_questions_fk", "tableFrom": "course_quizzes_rels", "tableTo": "quiz_questions", "schemaTo": "payload", "columnsFrom": ["quiz_questions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._course_quizzes_v": {"name": "_course_quizzes_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_title": {"name": "version_title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_course_id_id": {"name": "version_course_id_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_pass_threshold": {"name": "version_pass_threshold", "type": "numeric", "primaryKey": false, "notNull": false, "default": 70}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__course_quizzes_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_course_quizzes_v_parent_idx": {"name": "_course_quizzes_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_version_version_slug_idx": {"name": "_course_quizzes_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_version_version_course_id_idx": {"name": "_course_quizzes_v_version_version_course_id_idx", "columns": [{"expression": "version_course_id_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_version_version_updated_at_idx": {"name": "_course_quizzes_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_version_version_created_at_idx": {"name": "_course_quizzes_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_version_version__status_idx": {"name": "_course_quizzes_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_created_at_idx": {"name": "_course_quizzes_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_updated_at_idx": {"name": "_course_quizzes_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_latest_idx": {"name": "_course_quizzes_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_course_quizzes_v_parent_id_course_quizzes_id_fk": {"name": "_course_quizzes_v_parent_id_course_quizzes_id_fk", "tableFrom": "_course_quizzes_v", "tableTo": "course_quizzes", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "_course_quizzes_v_version_course_id_id_courses_id_fk": {"name": "_course_quizzes_v_version_course_id_id_courses_id_fk", "tableFrom": "_course_quizzes_v", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["version_course_id_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._course_quizzes_v_rels": {"name": "_course_quizzes_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "quiz_questions_id": {"name": "quiz_questions_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_course_quizzes_v_rels_order_idx": {"name": "_course_quizzes_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_rels_parent_idx": {"name": "_course_quizzes_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_rels_path_idx": {"name": "_course_quizzes_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_course_quizzes_v_rels_quiz_questions_id_idx": {"name": "_course_quizzes_v_rels_quiz_questions_id_idx", "columns": [{"expression": "quiz_questions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_course_quizzes_v_rels_parent_fk": {"name": "_course_quizzes_v_rels_parent_fk", "tableFrom": "_course_quizzes_v_rels", "tableTo": "_course_quizzes_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_course_quizzes_v_rels_quiz_questions_fk": {"name": "_course_quizzes_v_rels_quiz_questions_fk", "tableFrom": "_course_quizzes_v_rels", "tableTo": "quiz_questions", "schemaTo": "payload", "columnsFrom": ["quiz_questions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.quiz_questions_options": {"name": "quiz_questions_options", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "is_correct": {"name": "is_correct", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}}, "indexes": {"quiz_questions_options_order_idx": {"name": "quiz_questions_options_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_questions_options_parent_id_idx": {"name": "quiz_questions_options_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_questions_options_parent_id_fk": {"name": "quiz_questions_options_parent_id_fk", "tableFrom": "quiz_questions_options", "tableTo": "quiz_questions", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.quiz_questions": {"name": "quiz_questions", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question": {"name": "question", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "enum_quiz_questions_type", "typeSchema": "payload", "primaryKey": false, "notNull": true, "default": "'multiple_choice'"}, "question_slug": {"name": "question_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "explanation": {"name": "explanation", "type": "jsonb", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_questions_question_slug_idx": {"name": "quiz_questions_question_slug_idx", "columns": [{"expression": "question_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_questions_updated_at_idx": {"name": "quiz_questions_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_questions_created_at_idx": {"name": "quiz_questions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.survey_questions_options": {"name": "survey_questions_options", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "option": {"name": "option", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"survey_questions_options_order_idx": {"name": "survey_questions_options_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "survey_questions_options_parent_id_idx": {"name": "survey_questions_options_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"survey_questions_options_parent_id_fk": {"name": "survey_questions_options_parent_id_fk", "tableFrom": "survey_questions_options", "tableTo": "survey_questions", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.survey_questions": {"name": "survey_questions", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_slug": {"name": "question_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "enum_survey_questions_type", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'multiple_choice'"}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "required": {"name": "required", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "questionspin": {"name": "questionspin", "type": "enum_survey_questions_questionspin", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'Positive'"}, "position": {"name": "position", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_survey_questions_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"survey_questions_question_slug_idx": {"name": "survey_questions_question_slug_idx", "columns": [{"expression": "question_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "survey_questions_updated_at_idx": {"name": "survey_questions_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "survey_questions_created_at_idx": {"name": "survey_questions_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "survey_questions__status_idx": {"name": "survey_questions__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._survey_questions_v_version_options": {"name": "_survey_questions_v_version_options", "schema": "payload", "columns": {"_order": {"name": "_order", "type": "integer", "primaryKey": false, "notNull": true}, "_parent_id": {"name": "_parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "option": {"name": "option", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "_uuid": {"name": "_uuid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}}, "indexes": {"_survey_questions_v_version_options_order_idx": {"name": "_survey_questions_v_version_options_order_idx", "columns": [{"expression": "_order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_version_options_parent_id_idx": {"name": "_survey_questions_v_version_options_parent_id_idx", "columns": [{"expression": "_parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_survey_questions_v_version_options_parent_id_fk": {"name": "_survey_questions_v_version_options_parent_id_fk", "tableFrom": "_survey_questions_v_version_options", "tableTo": "_survey_questions_v", "schemaTo": "payload", "columnsFrom": ["_parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._survey_questions_v": {"name": "_survey_questions_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_question_slug": {"name": "version_question_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_text": {"name": "version_text", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_type": {"name": "version_type", "type": "enum__survey_questions_v_version_type", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'multiple_choice'"}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_required": {"name": "version_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "version_category": {"name": "version_category", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_questionspin": {"name": "version_questionspin", "type": "enum__survey_questions_v_version_questionspin", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'Positive'"}, "version_position": {"name": "version_position", "type": "numeric", "primaryKey": false, "notNull": false, "default": 0}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__survey_questions_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_survey_questions_v_parent_idx": {"name": "_survey_questions_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_version_version_question_slug_idx": {"name": "_survey_questions_v_version_version_question_slug_idx", "columns": [{"expression": "version_question_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_version_version_updated_at_idx": {"name": "_survey_questions_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_version_version_created_at_idx": {"name": "_survey_questions_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_version_version__status_idx": {"name": "_survey_questions_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_created_at_idx": {"name": "_survey_questions_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_updated_at_idx": {"name": "_survey_questions_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_survey_questions_v_latest_idx": {"name": "_survey_questions_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_survey_questions_v_parent_id_survey_questions_id_fk": {"name": "_survey_questions_v_parent_id_survey_questions_id_fk", "tableFrom": "_survey_questions_v", "tableTo": "survey_questions", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.surveys": {"name": "surveys", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "enum_surveys_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "published_at": {"name": "published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "_status": {"name": "_status", "type": "enum_surveys_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}}, "indexes": {"surveys_slug_idx": {"name": "surveys_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "surveys_updated_at_idx": {"name": "surveys_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys_created_at_idx": {"name": "surveys_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys__status_idx": {"name": "surveys__status_idx", "columns": [{"expression": "_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.surveys_rels": {"name": "surveys_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"surveys_rels_order_idx": {"name": "surveys_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys_rels_parent_idx": {"name": "surveys_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys_rels_path_idx": {"name": "surveys_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "surveys_rels_downloads_id_idx": {"name": "surveys_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"surveys_rels_parent_fk": {"name": "surveys_rels_parent_fk", "tableFrom": "surveys_rels", "tableTo": "surveys", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "surveys_rels_downloads_fk": {"name": "surveys_rels_downloads_fk", "tableFrom": "surveys_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._surveys_v": {"name": "_surveys_v", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "version_slug": {"name": "version_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_description": {"name": "version_description", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "version_status": {"name": "version_status", "type": "enum__surveys_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "version_published_at": {"name": "version_published_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_updated_at": {"name": "version_updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version_created_at": {"name": "version_created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": false}, "version__status": {"name": "version__status", "type": "enum__surveys_v_version_status", "typeSchema": "payload", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "latest": {"name": "latest", "type": "boolean", "primaryKey": false, "notNull": false}}, "indexes": {"_surveys_v_parent_idx": {"name": "_surveys_v_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_version_version_slug_idx": {"name": "_surveys_v_version_version_slug_idx", "columns": [{"expression": "version_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_version_version_updated_at_idx": {"name": "_surveys_v_version_version_updated_at_idx", "columns": [{"expression": "version_updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_version_version_created_at_idx": {"name": "_surveys_v_version_version_created_at_idx", "columns": [{"expression": "version_created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_version_version__status_idx": {"name": "_surveys_v_version_version__status_idx", "columns": [{"expression": "version__status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_created_at_idx": {"name": "_surveys_v_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_updated_at_idx": {"name": "_surveys_v_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_latest_idx": {"name": "_surveys_v_latest_idx", "columns": [{"expression": "latest", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_surveys_v_parent_id_surveys_id_fk": {"name": "_surveys_v_parent_id_surveys_id_fk", "tableFrom": "_surveys_v", "tableTo": "surveys", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload._surveys_v_rels": {"name": "_surveys_v_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"_surveys_v_rels_order_idx": {"name": "_surveys_v_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_rels_parent_idx": {"name": "_surveys_v_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_rels_path_idx": {"name": "_surveys_v_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "_surveys_v_rels_downloads_id_idx": {"name": "_surveys_v_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"_surveys_v_rels_parent_fk": {"name": "_surveys_v_rels_parent_fk", "tableFrom": "_surveys_v_rels", "tableTo": "_surveys_v", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "_surveys_v_rels_downloads_fk": {"name": "_surveys_v_rels_downloads_fk", "tableFrom": "_surveys_v_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.payload_locked_documents": {"name": "payload_locked_documents", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "global_slug": {"name": "global_slug", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_locked_documents_global_slug_idx": {"name": "payload_locked_documents_global_slug_idx", "columns": [{"expression": "global_slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_updated_at_idx": {"name": "payload_locked_documents_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_created_at_idx": {"name": "payload_locked_documents_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.payload_locked_documents_rels": {"name": "payload_locked_documents_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "uuid", "primaryKey": false, "notNull": false}, "media_id": {"name": "media_id", "type": "uuid", "primaryKey": false, "notNull": false}, "downloads_id": {"name": "downloads_id", "type": "uuid", "primaryKey": false, "notNull": false}, "posts_id": {"name": "posts_id", "type": "uuid", "primaryKey": false, "notNull": false}, "documentation_id": {"name": "documentation_id", "type": "uuid", "primaryKey": false, "notNull": false}, "private_id": {"name": "private_id", "type": "uuid", "primaryKey": false, "notNull": false}, "courses_id": {"name": "courses_id", "type": "uuid", "primaryKey": false, "notNull": false}, "course_lessons_id": {"name": "course_lessons_id", "type": "uuid", "primaryKey": false, "notNull": false}, "course_quizzes_id": {"name": "course_quizzes_id", "type": "uuid", "primaryKey": false, "notNull": false}, "quiz_questions_id": {"name": "quiz_questions_id", "type": "uuid", "primaryKey": false, "notNull": false}, "survey_questions_id": {"name": "survey_questions_id", "type": "uuid", "primaryKey": false, "notNull": false}, "surveys_id": {"name": "surveys_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"payload_locked_documents_rels_order_idx": {"name": "payload_locked_documents_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_parent_idx": {"name": "payload_locked_documents_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_path_idx": {"name": "payload_locked_documents_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_users_id_idx": {"name": "payload_locked_documents_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_media_id_idx": {"name": "payload_locked_documents_rels_media_id_idx", "columns": [{"expression": "media_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_downloads_id_idx": {"name": "payload_locked_documents_rels_downloads_id_idx", "columns": [{"expression": "downloads_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_posts_id_idx": {"name": "payload_locked_documents_rels_posts_id_idx", "columns": [{"expression": "posts_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_documentation_id_idx": {"name": "payload_locked_documents_rels_documentation_id_idx", "columns": [{"expression": "documentation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_private_id_idx": {"name": "payload_locked_documents_rels_private_id_idx", "columns": [{"expression": "private_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_courses_id_idx": {"name": "payload_locked_documents_rels_courses_id_idx", "columns": [{"expression": "courses_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_course_lessons_id_idx": {"name": "payload_locked_documents_rels_course_lessons_id_idx", "columns": [{"expression": "course_lessons_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_course_quizzes_id_idx": {"name": "payload_locked_documents_rels_course_quizzes_id_idx", "columns": [{"expression": "course_quizzes_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_quiz_questions_id_idx": {"name": "payload_locked_documents_rels_quiz_questions_id_idx", "columns": [{"expression": "quiz_questions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_survey_questions_id_idx": {"name": "payload_locked_documents_rels_survey_questions_id_idx", "columns": [{"expression": "survey_questions_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_locked_documents_rels_surveys_id_idx": {"name": "payload_locked_documents_rels_surveys_id_idx", "columns": [{"expression": "surveys_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_locked_documents_rels_parent_fk": {"name": "payload_locked_documents_rels_parent_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "payload_locked_documents", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_users_fk": {"name": "payload_locked_documents_rels_users_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "users", "schemaTo": "payload", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_media_fk": {"name": "payload_locked_documents_rels_media_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "media", "schemaTo": "payload", "columnsFrom": ["media_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_downloads_fk": {"name": "payload_locked_documents_rels_downloads_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "downloads", "schemaTo": "payload", "columnsFrom": ["downloads_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_posts_fk": {"name": "payload_locked_documents_rels_posts_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "posts", "schemaTo": "payload", "columnsFrom": ["posts_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_documentation_fk": {"name": "payload_locked_documents_rels_documentation_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "documentation", "schemaTo": "payload", "columnsFrom": ["documentation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_private_fk": {"name": "payload_locked_documents_rels_private_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "private", "schemaTo": "payload", "columnsFrom": ["private_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_courses_fk": {"name": "payload_locked_documents_rels_courses_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "courses", "schemaTo": "payload", "columnsFrom": ["courses_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_course_lessons_fk": {"name": "payload_locked_documents_rels_course_lessons_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "course_lessons", "schemaTo": "payload", "columnsFrom": ["course_lessons_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_course_quizzes_fk": {"name": "payload_locked_documents_rels_course_quizzes_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "course_quizzes", "schemaTo": "payload", "columnsFrom": ["course_quizzes_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_quiz_questions_fk": {"name": "payload_locked_documents_rels_quiz_questions_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "quiz_questions", "schemaTo": "payload", "columnsFrom": ["quiz_questions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_survey_questions_fk": {"name": "payload_locked_documents_rels_survey_questions_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "survey_questions", "schemaTo": "payload", "columnsFrom": ["survey_questions_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_locked_documents_rels_surveys_fk": {"name": "payload_locked_documents_rels_surveys_fk", "tableFrom": "payload_locked_documents_rels", "tableTo": "surveys", "schemaTo": "payload", "columnsFrom": ["surveys_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.payload_preferences": {"name": "payload_preferences", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_preferences_key_idx": {"name": "payload_preferences_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_updated_at_idx": {"name": "payload_preferences_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_created_at_idx": {"name": "payload_preferences_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.payload_preferences_rels": {"name": "payload_preferences_rels", "schema": "payload", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": true}, "path": {"name": "path", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "users_id": {"name": "users_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {"payload_preferences_rels_order_idx": {"name": "payload_preferences_rels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_parent_idx": {"name": "payload_preferences_rels_parent_idx", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_path_idx": {"name": "payload_preferences_rels_path_idx", "columns": [{"expression": "path", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_preferences_rels_users_id_idx": {"name": "payload_preferences_rels_users_id_idx", "columns": [{"expression": "users_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payload_preferences_rels_parent_fk": {"name": "payload_preferences_rels_parent_fk", "tableFrom": "payload_preferences_rels", "tableTo": "payload_preferences", "schemaTo": "payload", "columnsFrom": ["parent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "payload_preferences_rels_users_fk": {"name": "payload_preferences_rels_users_fk", "tableFrom": "payload_preferences_rels", "tableTo": "users", "schemaTo": "payload", "columnsFrom": ["users_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "payload.payload_migrations": {"name": "payload_migrations", "schema": "payload", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "batch": {"name": "batch", "type": "numeric", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp(3) with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"payload_migrations_updated_at_idx": {"name": "payload_migrations_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "payload_migrations_created_at_idx": {"name": "payload_migrations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"payload.enum_users_role": {"name": "enum_users_role", "schema": "payload", "values": ["admin", "user"]}, "payload.enum_media_type": {"name": "enum_media_type", "schema": "payload", "values": ["image", "video", "document"]}, "payload.enum_downloads_category": {"name": "enum_downloads_category", "schema": "payload", "values": ["document", "template", "resource", "software", "media", "archive", "other"]}, "payload.enum_downloads_access_level": {"name": "enum_downloads_access_level", "schema": "payload", "values": ["public", "registered", "premium"]}, "payload.enum_posts_status": {"name": "enum_posts_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__posts_v_version_status": {"name": "enum__posts_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_documentation_status": {"name": "enum_documentation_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__documentation_v_version_status": {"name": "enum__documentation_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_private_status": {"name": "enum_private_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__private_v_version_status": {"name": "enum__private_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_courses_status": {"name": "enum_courses_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__courses_v_version_status": {"name": "enum__courses_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_course_lessons_video_source_type": {"name": "enum_course_lessons_video_source_type", "schema": "payload", "values": ["youtube", "vimeo"]}, "payload.enum_course_lessons_status": {"name": "enum_course_lessons_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__course_lessons_v_version_video_source_type": {"name": "enum__course_lessons_v_version_video_source_type", "schema": "payload", "values": ["youtube", "vimeo"]}, "payload.enum__course_lessons_v_version_status": {"name": "enum__course_lessons_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_course_quizzes_status": {"name": "enum_course_quizzes_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__course_quizzes_v_version_status": {"name": "enum__course_quizzes_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_quiz_questions_type": {"name": "enum_quiz_questions_type", "schema": "payload", "values": ["multiple_choice"]}, "payload.enum_survey_questions_type": {"name": "enum_survey_questions_type", "schema": "payload", "values": ["multiple_choice", "text_field", "scale"]}, "payload.enum_survey_questions_questionspin": {"name": "enum_survey_questions_questionspin", "schema": "payload", "values": ["Positive", "Negative"]}, "payload.enum_survey_questions_status": {"name": "enum_survey_questions_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__survey_questions_v_version_type": {"name": "enum__survey_questions_v_version_type", "schema": "payload", "values": ["multiple_choice", "text_field", "scale"]}, "payload.enum__survey_questions_v_version_questionspin": {"name": "enum__survey_questions_v_version_questionspin", "schema": "payload", "values": ["Positive", "Negative"]}, "payload.enum__survey_questions_v_version_status": {"name": "enum__survey_questions_v_version_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum_surveys_status": {"name": "enum_surveys_status", "schema": "payload", "values": ["draft", "published"]}, "payload.enum__surveys_v_version_status": {"name": "enum__surveys_v_version_status", "schema": "payload", "values": ["draft", "published"]}}, "schemas": {"payload": "payload"}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}}