{"name": "@kit/stripe", "private": true, "version": "0.1.0", "scripts": {"clean": "git clean -xdf .turbo node_modules", "format": "prettier --check \"**/*.{ts,tsx}\"", "lint": "eslint .", "typecheck": "tsc --noEmit", "start": "docker run --rm -it --name=stripe -v ~/.config/stripe:/root/.config/stripe stripe/stripe-cli:latest listen --forward-to http://host.docker.internal:3000/api/billing/webhook"}, "prettier": "@kit/prettier-config", "exports": {".": "./src/index.ts", "./components": "./src/components/index.ts"}, "dependencies": {"@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "stripe": "^18.1.1"}, "devDependencies": {"@kit/billing": "workspace:*", "@kit/eslint-config": "workspace:*", "@kit/prettier-config": "workspace:*", "@kit/shared": "workspace:*", "@kit/supabase": "workspace:*", "@kit/tsconfig": "workspace:*", "@kit/ui": "workspace:*", "@types/react": "19.1.6", "date-fns": "^4.1.0", "next": "15.3.2", "react": "19.1.0", "zod": "^3.25.31"}, "typesVersions": {"*": {"*": ["src/*"]}}}